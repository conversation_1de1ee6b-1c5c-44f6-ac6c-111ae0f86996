{"1": {"inputs": {"ckpt_name": "wan2.2-i2v-rapid-aio-v9.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "2": {"inputs": {"shift": 6.000000000000001, "model": ["1", 0]}, "class_type": "ModelSamplingSD3", "_meta": {"title": "ModelSamplingSD3"}}, "3": {"inputs": {"seed": "{{seed}}", "steps": 6, "cfg": 1, "sampler_name": "euler_ancestral", "scheduler": "beta", "denoise": 1, "model": ["2", 0], "positive": ["9", 0], "negative": ["9", 1], "latent_image": ["9", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"text": "{{negative_prompt}}", "clip": ["1", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Negative (leave blank)"}}, "5": {"inputs": {"text": "{{positive_prompt}}", "clip": ["1", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "Positive Input"}}, "7": {"inputs": {"samples": ["3", 0], "vae": ["1", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "9": {"inputs": {"width": "{{width}}", "height": "{{height}}", "length": "{{frames}}", "batch_size": 1, "positive": ["5", 0], "negative": ["4", 0], "vae": ["1", 2], "clip_vision_output": ["11", 0], "start_image": ["10", 0]}, "class_type": "WanImageToVideo", "_meta": {"title": "WanImageToVideo"}}, "10": {"inputs": {"image": "{{input_image}}"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "11": {"inputs": {"crop": "center", "clip_vision": ["12", 0], "image": ["10", 0]}, "class_type": "CLIPVisionEncode", "_meta": {"title": "CLIP Vision Encode"}}, "12": {"inputs": {"clip_name": "clip_vision_h.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "18": {"inputs": {"frame_rate": "{{fps}}", "loop_count": 0, "filename_prefix": "{{filename_prefix}}", "format": "video/h264-mp4", "pix_fmt": "yuv420p", "crf": 19, "save_metadata": true, "trim_to_audio": false, "pingpong": false, "save_output": true, "images": ["7", 0], "vae": ["1", 2]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}}